<template>
  <div class="network-device-monitor">
    <!-- 使用splitpane组件，设置左右分割的比例 -->
    <splitpane :splitSet="settingLR">
      <!-- 左侧面板 -->
      <template #paneL>
        <div class="left-panel">
          <div class="org-header">
            <h3>所属组织</h3>
          </div>
          <div class="org-tree">
            <el-input
              v-model="orgSearchKeyword"
              placeholder="请输入"
              prefix-icon="Search"
              size="small"
              clearable
            />
            <el-tree
              ref="orgTreeRef"
              :data="orgTreeData"
              :props="{ children: 'children', label: 'name' }"
              node-key="id"
              :default-expanded-keys="['1']"
              highlight-current
              :expand-on-click-node="false"
              :filter-node-method="filterOrgNode"
              :style="treeStyle"
              @node-click="handleOrgNodeClick"
            />
          </div>
        </div>
      </template>

      <!-- 右侧面板 -->
      <template #paneR>
        <div class="main-content">
      <!-- Tab切换 - 按原型图样式 -->
      <div class="tab-container">
        <div
          v-for="tab in tabConfigs"
          :key="tab.key"
          :class="['tab-item', { active: state.activeTab === tab.key }]"
          @click="switchTab(tab.key)"
        >
          {{ tab.label }}
        </div>
      </div>

      <!-- 统计卡片区域 -->
      <div class="statistics-section">
        <div
          v-for="(stat, index) in (currentStatistics || [])"
          :key="index"
          class="stat-card"
        >
          <div class="stat-header">
            <span class="stat-title">{{ stat.title }}</span>
            <el-icon v-if="stat.title.includes('超7天')" class="help-icon">
              <QuestionFilled />
            </el-icon>
          </div>
          <div class="stat-value">{{ stat.value }}</div>
          <div class="stat-footer">
            <div v-if="stat.trend" class="stat-trend">
              <span class="trend-label">{{ stat.trend.period }}:</span>
              <span :class="['trend-value', stat.trend.type]">
                {{ stat.trend.type === 'up' ? '+' : '' }}{{ stat.trend.value }}
              </span>
            </div>
            <div v-if="stat.chart" class="stat-chart">
              <div class="mini-chart" :style="{ backgroundColor: stat.chart.color }"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 趋势图表区域 -->
      <div class="chart-section">
        <div class="chart-header">
          <h3>{{ currentTrendChart.title || '设备在线趋势' }}</h3>
          <div class="chart-filters">
            <el-select v-model="chartFilters.platform" placeholder="安全运维平台" size="small">
              <el-option
                v-for="option in (currentTrendChart.filters?.platform || [])"
                :key="option"
                :label="option"
                :value="option"
              />
            </el-select>
            <el-select v-model="chartFilters.location" placeholder="张三" size="small">
              <el-option
                v-for="option in (currentTrendChart.filters?.location || [])"
                :key="option"
                :label="option"
                :value="option"
              />
            </el-select>
            <el-select v-model="chartFilters.timeRange" placeholder="东莞中心医院" size="small">
              <el-option
                v-for="option in (currentTrendChart.filters?.timeRange || [])"
                :key="option"
                :label="option"
                :value="option"
              />
            </el-select>
            <el-select v-model="chartFilters.period" placeholder="近24小时" size="small">
              <el-option label="近24小时" value="24h" />
              <el-option label="近7天" value="7d" />
              <el-option label="近30天" value="30d" />
            </el-select>
          </div>
        </div>
        <div class="chart-content">
          <!-- 这里集成ECharts图表组件 -->
          <div class="trend-chart" ref="trendChartRef"></div>
        </div>
      </div>

      <!-- 设备列表区域 -->
      <div class="device-list-section">
        <div class="list-header">
          <h3>{{ state.activeTab === 'server' ? '服务器列表' : '终端列表' }}</h3>
          <div class="list-actions">
            <search-with-column
              v-model="columnCondition.value"
              v-model:fuzzy-enable="columnCondition.fuzzyable"
              v-model:column-val="columnCondition.field"
              :column-options="columnSearchOptions"
              :column-select-width="90"
              @search="handleSearch"
              @reset="handleReset"
              class="flex-c w-4/5"
              input-class-name="w-3/5"
            />
          </div>
        </div>

        <div class="action-buttons">
          <el-button type="primary" size="small">
            <el-icon><Plus /></el-icon>
            新增资产
          </el-button>
          <el-button size="small">批量删除</el-button>
          <el-button size="small">任务配置</el-button>
          <el-button size="small">导出</el-button>
        </div>

        <!-- 设备表格 -->
        <im-table
          ref="tableRef"
          :data="tableData"
          :columns="tableColumns"
          center
          toolbar
          :table-alert="{
            closable: false
          }"
          operator
          :height="tableOption.height"
          :stripe="tableOption.stripe"
          show-checkbox
          :pagination="tablePage"
          :loading="tableLoading"
          :filter-data-provider="filterDataProvider"
          @on-reload="resetTablePageAndQuery"
          @selection-change="selectionChangeHandler"
          @on-page-change="loadDeviceList"
        >
          <!-- 表格操作按钮 -->
          <template #operator="scope: any">
            <el-button
              :size="scope.size"
              type="primary"
              text
              :icon="useRenderIcon('EP-View')"
              @click="handleOperation('查看', scope.row)"
            >
              详情
            </el-button>
            <el-button
              v-for="operation in (scope.row.operations || [])"
              :key="operation"
              :size="scope.size"
              type="primary"
              text
              @click="handleOperation(operation, scope.row)"
            >
              {{ operation }}
            </el-button>
          </template>

          <!-- 状态列 -->
          <template #status="{ row }">
            <div class="status-indicator">
              <span :class="['status-dot', getStatusClass(row.status)]"></span>
              {{ getStatusText(row.status) }}
            </div>
          </template>
        </im-table>
      </div>
        </div>
      </template>
    </splitpane>


  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted, onBeforeUnmount, nextTick, watch, CSSProperties } from 'vue';
import { ElMessage, ElTree } from 'element-plus';
import { QuestionFilled, Plus } from '@element-plus/icons-vue';
import * as echarts from 'echarts';
import splitpane, { ContextProps } from "@/components/ReSplitPane";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import SearchWithColumn from "@/components/Search/SearchWithColumnAddSelect.vue";
import ImTable from "@/components/ItsmCommon/table/ImTable.vue";
import {
  HeaderFilterValue,
  ImTableInstance
} from "@/components/ItsmCommon";
import {
  FilterOption,
  TableFilterDataProvider
} from "@/components/ItsmCommon/table/props";
import { defaultPageSize, pageSizeOptions } from "@/utils/page_util";
import {
  DeviceTabType,
  DeviceStatus,
  StatisticCard,
  TrendChartConfig,
  HomePageState,
  OrgTreeNode
} from '../types';
import apiService from '../api';

// splitpane 配置
const settingLR: ContextProps = reactive({
  minPercent: 10,
  defaultPercent: 20,
  split: "vertical"
});

// 组件引用
const orgTreeRef = ref<InstanceType<typeof ElTree> | null>(null);
const tableRef = ref<ImTableInstance>();

// 响应式数据
const state = reactive<HomePageState>({
  activeTab: DeviceTabType.SERVER,
  serverDevices: [],
  terminalDevices: [],
  tableFilters: {
    searchKeyword: ''
  },
  pagination: {
    current: 1,
    pageSize: 20,
    total: 0
  },
  loading: false
});

// 其他响应式数据
const orgSearchKeyword = ref('');
const chartFilters = reactive({
  platform: '',
  location: '',
  timeRange: '',
  period: '24h'
});

// 搜索相关状态
const columnCondition = reactive({
  value: null,
  field: "ipAddress", // 默认搜索IP地址
  fuzzyable: true,
  operator: "fuzzy"
});

const columnSearchOptions = ref([
  { label: "IP地址", value: "ipAddress" },
  { label: "MAC地址", value: "macAddress" },
  { label: "资产责任人", value: "assetOwner" },
  { label: "所在位置", value: "location" }
]);

// 表格相关状态
const tableData = ref([]);
const tableLoading = ref(false);
const tablePage = reactive({
  align: "right",
  total: 0,
  current: 1,
  currentPage: 1,
  pageSize: defaultPageSize,
  pageSizes: pageSizeOptions
});

const tableOption = reactive({
  align: "center",
  menuAlign: "center",
  border: true,
  stripe: true,
  selection: true,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  menuWidth: 130,
  height: 600,
  rowKey: "id",
  column: [
    {
      label: "IP地址",
      prop: "ipAddress",
      width: 140,
      sortable: true
    },
    {
      label: "MAC地址",
      prop: "macAddress",
      width: 160,
      sortable: true
    },
    {
      label: "业务系统",
      prop: "businessSystem",
      width: 160,
      sortable: true
    },
    {
      label: "资产责任人",
      prop: "assetOwner",
      width: 120,
      sortable: true
    },
    {
      label: "所在位置",
      prop: "location",
      width: 140,
      sortable: true
    },
    {
      label: "在线时长",
      prop: "onlineDuration",
      width: 100,
      sortable: true
    },
    {
      label: "状态",
      prop: "status",
      width: 100,
      slot: true
    }
  ]
});

// 添加过滤器状态
const filters = ref([]);

// 统计数据
const serverStatistics = ref<StatisticCard[]>([]);
const terminalStatistics = ref<StatisticCard[]>([]);
const serverTrendChart = ref<TrendChartConfig>({
  title: '服务器在线趋势',
  data: [],
  filters: {
    platform: [],
    location: [],
    timeRange: []
  }
});
const terminalTrendChart = ref<TrendChartConfig>({
  title: '终端在线趋势',
  data: [],
  filters: {
    platform: [],
    location: [],
    timeRange: []
  }
});

// 图表引用
const trendChartRef = ref<HTMLElement>();
let chartInstance: echarts.ECharts | null = null;

// 事件监听器引用
let chartResizeHandler: (() => void) | null = null;

// 组织架构数据
const orgTreeData = ref<OrgTreeNode[]>([
  {
    id: '1',
    name: '东莞市中心医院',
    children: [
      { id: '1-1', name: '东莞中心医院总院' },
      { id: '1-2', name: '东莞中心医院分院' }
    ]
  }
]);

// Tab配置
const tabConfigs = [
  { key: DeviceTabType.SERVER, label: '服务器设备' },
  { key: DeviceTabType.TERMINAL, label: '终端设备' }
];

// 计算属性
const currentStatistics = computed(() => {
  return state.activeTab === DeviceTabType.SERVER ? serverStatistics.value : terminalStatistics.value;
});

const currentTrendChart = computed(() => {
  return state.activeTab === DeviceTabType.SERVER ? serverTrendChart.value : terminalTrendChart.value;
});

// 树样式计算属性
const treeStyle = computed((): CSSProperties => {
  return {
    height: "calc(100vh - 200px)",
    overflow: "auto"
  };
});

// 组织树过滤方法
const filterOrgNode = (value: string, data: OrgTreeNode) => {
  if (!value) return true;
  return data.name.indexOf(value) !== -1;
};

// 监听搜索关键词变化
watch(() => orgSearchKeyword.value, (val) => {
  orgTreeRef.value?.filter(val);
});

// 表格相关方法
const resetTablePageAndQuery = (keepFiltersFlag?: boolean) => {
  if (keepFiltersFlag !== true) {
    // 清除表格所有过滤器
    filters.value = [];
    tableRef.value?.clearAllFilters();
  }
  tablePage.current = 1;
  tablePage.currentPage = 1;
  loadDeviceList();
};

const selectionChangeHandler = (selRows: any[]) => {
  // 处理选择变化
  console.log('选中的行:', selRows);
};

// 表格过滤数据提供者
const filterDataProvider: TableFilterDataProvider = {
  options: (_prop: string, _filters: HeaderFilterValue[]) => {
    return new Promise<{ total: number; options: FilterOption[] }>(resolve => {
      // 这里应该调用实际的API来获取过滤选项
      // 暂时返回空数据，避免错误
      resolve({ total: 0, options: [] });
    });
  },
  onFilter: (filterValues: HeaderFilterValue[]) => {
    filters.value = filterValues;
    resetTablePageAndQuery(true); // 保持过滤器
  }
};

// 方法定义
const switchTab = async (tabKey: DeviceTabType) => {
  state.activeTab = tabKey;
  await loadDeviceList();
  await nextTick();
  initChart();
};

const handleOrgNodeClick = (_data: OrgTreeNode) => {
  loadDeviceList();
};

const getStatusClass = (status: DeviceStatus) => ({
  [DeviceStatus.ONLINE]: 'online',
  [DeviceStatus.OFFLINE]: 'offline',
  [DeviceStatus.FAULT]: 'fault',
  [DeviceStatus.MAINTENANCE]: 'maintenance'
}[status] || 'offline');

const getStatusText = (status: DeviceStatus) => ({
  [DeviceStatus.ONLINE]: '在线',
  [DeviceStatus.OFFLINE]: '离线',
  [DeviceStatus.FAULT]: '故障',
  [DeviceStatus.MAINTENANCE]: '维护中'
}[status] || '未知');

const handleSearch = () => {
  tablePage.current = 1;
  tablePage.currentPage = 1;
  state.pagination.current = 1; // 保持兼容性
  loadDeviceList();
};

const handleReset = () => {
  columnCondition.value = null;
  columnCondition.field = "ipAddress";
  columnCondition.fuzzyable = true;
  tablePage.current = 1;
  tablePage.currentPage = 1;
  state.tableFilters.searchKeyword = '';
  state.pagination.current = 1;
  loadDeviceList();
};

const handleOperation = async (operation: string, device: any) => {
  try {
    if (operation === '查看') {
      ElMessage.info('查看设备详情功能待开发');
      return;
    }

    const actions = {
      '在线状态': () => apiService.operation.toggleOnlineStatus(device.id),
      '移除': () => apiService.operation.removeDevice(device.id)
    };

    await actions[operation]?.();
    ElMessage.success(`${operation}成功`);
    loadDeviceList();
  } catch (error) {
    ElMessage.error('操作失败');
  }
};



// 数据加载方法
const loadStatistics = async () => {
  try {
    const [serverStats, terminalStats] = await Promise.all([
      apiService.server.getStatistics(),
      apiService.terminal.getStatistics()
    ]);

    if (serverStats.success) {
      serverStatistics.value = serverStats.data;
    }

    if (terminalStats.success) {
      terminalStatistics.value = terminalStats.data;
    }
  } catch (error) {
    ElMessage.error('加载统计数据失败');
  }
};

const loadTrendCharts = async () => {
  try {
    const [serverChart, terminalChart] = await Promise.all([
      apiService.server.getTrendChart(),
      apiService.terminal.getTrendChart()
    ]);

    if (serverChart.success) {
      serverTrendChart.value = serverChart.data;
    }

    if (terminalChart.success) {
      terminalTrendChart.value = terminalChart.data;
    }
  } catch (error) {
    ElMessage.error('加载图表数据失败');
  }
};

const loadDeviceList = async () => {
  tableLoading.value = true;
  try {
    const api = state.activeTab === DeviceTabType.SERVER ? apiService.server : apiService.terminal;

    // 构建查询参数
    const queryParams = {
      searchKeyword: columnCondition.value,
      searchField: columnCondition.field,
      fuzzy: columnCondition.fuzzyable,
      pageNum: tablePage.current,
      pageSize: tablePage.pageSize,
      // 添加过滤器参数
      headerFilter: {
        filters: filters.value
      }
    };

    const response = await api.getDeviceList(queryParams, tablePage);

    if (response.success) {
      tableData.value = response.data.list || [];
      tablePage.total = response.data.pagination?.total || 0;

      // 同时更新原有的状态以保持兼容性
      if (state.activeTab === DeviceTabType.SERVER) {
        state.serverDevices = response.data.list as any[];
      } else {
        state.terminalDevices = response.data.list as any[];
      }
      state.pagination = response.data.pagination || {
        current: tablePage.current,
        pageSize: tablePage.pageSize,
        total: tablePage.total
      };
    }
  } catch (error) {
    ElMessage.error('加载设备列表失败');
  } finally {
    tableLoading.value = false;
    state.loading = false;
  }
};

// 图表初始化
const initChart = () => {
  if (!trendChartRef.value || !currentTrendChart.value.data) return;

  if (chartInstance) {
    chartInstance.dispose();
  }

  chartInstance = echarts.init(trendChartRef.value);

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    legend: {
      data: ['在线数量', '总数量']
    },
    xAxis: {
      type: 'category',
      data: currentTrendChart.value.data.map(item => item.date)
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '在线数量',
        type: 'line',
        data: currentTrendChart.value.data.map(item => item.onlineCount),
        itemStyle: { color: '#52c41a' }
      },
      {
        name: '总数量',
        type: 'line',
        data: currentTrendChart.value.data.map(item => item.totalCount),
        itemStyle: { color: '#1890ff' }
      }
    ]
  };

  chartInstance.setOption(option);

  // 添加窗口大小变化监听器
  if (chartResizeHandler) {
    window.removeEventListener('resize', chartResizeHandler);
  }
  chartResizeHandler = () => {
    chartInstance?.resize();
  };
  window.addEventListener('resize', chartResizeHandler);
};



// 生命周期
onMounted(async () => {
  await Promise.all([
    loadStatistics(),
    loadTrendCharts(),
    loadDeviceList()
  ]);

  await nextTick();
  initChart();
});

// 组件卸载前清理
onBeforeUnmount(() => {
  // 清理图表实例
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }

  // 移除窗口事件监听器
  if (chartResizeHandler) {
    window.removeEventListener('resize', chartResizeHandler);
    chartResizeHandler = null;
  }
});
</script>

<style scoped lang="scss">
.network-device-monitor {
  height: 100vh;

  .left-panel {
    padding: 16px;
    height: 100%;
    overflow: hidden;

    .org-header {
      margin-bottom: 16px;

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
      }
    }

    .org-tree {
      .el-input {
        margin-bottom: 12px;
      }

      .el-tree {
        background: transparent;
      }
    }
  }

  .main-content {
    padding: 16px;
    height: 100%;
    overflow-y: auto;

    .tab-container {
      display: flex;
      margin-bottom: 16px;
      border-bottom: 1px solid #e4e7ed;

      .tab-item {
        padding: 12px 24px;
        cursor: pointer;
        border-bottom: 2px solid transparent;
        color: #606266;
        font-weight: 500;
        transition: all 0.3s ease;

        &:hover {
          color: #409eff;
        }

        &.active {
          color: #409eff;
          border-bottom-color: #409eff;
        }
      }
    }

    .statistics-section {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 16px;
      margin-bottom: 16px;

      .stat-card {
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s ease;

        &:hover {
          transform: translateY(-2px);
        }

        .stat-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;

          .stat-title {
            font-size: 14px;
            font-weight: 500;
          }

          .el-icon-question {
            cursor: pointer;
          }
        }

        .stat-value {
          font-size: 32px;
          font-weight: 700;
          margin-bottom: 8px;
        }

        .stat-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .stat-trend {
            font-size: 12px;

            .trend-label {
              opacity: 0.7;
            }

            .trend-value {
              font-weight: 600;
              margin-left: 4px;

              &.up {
                color: #67c23a;
              }

              &.down {
                color: #f56c6c;
              }
            }
          }

          .stat-chart {
            .mini-chart {
              width: 60px;
              height: 20px;
              border-radius: 2px;
              opacity: 0.3;
            }
          }
        }
      }
    }

    .chart-section {
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 16px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .chart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;

        h3 {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: #303133;
        }

        .chart-filters {
          display: flex;
          gap: 12px;

          .el-select {
            width: 120px;
          }
        }
      }

      .chart-content {
        .trend-chart {
          width: 100%;
          height: 300px;
        }
      }
    }

    .device-list-section {
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .list-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        h3 {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
        }

        .list-actions {
          display: flex;
          gap: 12px;
          align-items: center;

          .el-select {
            width: 120px;
          }
        }
      }

      .action-buttons {
        margin-bottom: 16px;
        display: flex;
        gap: 8px;
      }

      .status-indicator {
        display: flex;
        align-items: center;
        gap: 6px;

        .status-dot {
          width: 8px;
          height: 8px;
          border-radius: 50%;

          &.online {
            background-color: #67c23a;
          }

          &.offline {
            background-color: #909399;
          }

          &.fault {
            background-color: #f56c6c;
          }

          &.maintenance {
            background-color: #e6a23c;
          }
        }
      }

      .pagination-wrapper {
        margin-top: 20px;
        display: flex;
        justify-content: center;
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .network-device-monitor {
    .main-content {
      .statistics-section {
        grid-template-columns: repeat(2, 1fr);
      }
    }
  }
}

@media (max-width: 768px) {
  .network-device-monitor {
    flex-direction: column;

    .left-sidebar {
      width: 100%;
      height: auto;
    }

    .main-content {
      .statistics-section {
        grid-template-columns: 1fr;
      }

      .chart-section .chart-header {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;

        .chart-filters {
          justify-content: center;
        }
      }

      .device-list-section .list-header {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
      }
    }
  }
}


</style>
